import { useState } from "react";
import { AgentList } from "./components/agent-list";
import { CreateAgentForm } from "./components/create-agent-form";
import { WorkflowList } from "./components/workflow-list";
import { RunMonitor } from "./components/run-monitor";

function App() {
  const [refreshSignal, setRefreshSignal] = useState(0);

  const handleAgentCreated = () => {
    setRefreshSignal(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-background font-sans antialiased">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-10">
          <h1 className="text-4xl font-bold tracking-tight text-foreground">
            Web Agent Framework
          </h1>
          <p className="text-muted-foreground mt-2">
            Create, manage, and monitor your agentic workflows.
          </p>
        </header>

        <main className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Left Column: Agent & Workflow Management */}
          <div className="lg:col-span-2 space-y-10">
            <section>
              <h2 className="text-2xl font-semibold tracking-tight mb-4">Agents</h2>
              <CreateAgentForm onAgentCreated={handleAgentCreated} />
              <div className="mt-6">
                <AgentList refreshSignal={refreshSignal} />
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold tracking-tight mb-4">Workflows</h2>
               <WorkflowList />
            </section>
          </div>

          {/* Right Column: Run Monitor */}
          <aside className="space-y-10">
             <section>
              <h2 className="text-2xl font-semibold tracking-tight mb-4">Monitor</h2>
              {/* Hardcode the first workflow ID for demonstration */}
              <RunMonitor workflowId="bd703075-b420-4f75-ac59-c987108f11fe" />
            </section>
          </aside>
        </main>
      </div>
    </div>
  );
}

export default App;
