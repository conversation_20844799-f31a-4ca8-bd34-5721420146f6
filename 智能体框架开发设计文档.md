# Web智能体框架开发设计文档

## 1. 项目概述

### 1.1 背景

随着多大语言模型 (LLM) 的能力提升，Agentic Workflow 成为构建复杂 AI 系统的主流范式。本项目旨在基于 **LangGraph**，提供一套前后端一体的 **Web 智能体框架**，用可视化方式帮助开发者与业务人员快速创建、部署并迭代智能体。

### 1.2 目标

1. **可视化配置**：通过拖拽式流程编辑器构建 Agent 与 Workflow；
2. **主动‑反思‑修正‑迭代**：内置 Reflection 节点与评估机制，充分演示 Agentic Workflow；
3. **工具调用全链路透明**：MCP 接口统一封装第三方/自研工具，实时回溯调用日志；
4. **多模型支持**：默认集成 **Ollama**，并提供统一的模型适配层切换 OpenAI、Anthropic 等；
5. **现代前端体验**：极简 UI、暗黑模式、自适应布局，支持快捷键与实时协作。

---

## 2. 技术栈

| 层级      | 主要技术                                                                        | 说明 (原型阶段)                               |
| ------- | --------------------------------------------------------------------------- | ------------------------------------------ |
| 前端      | React (＋TypeScript) · Vite · TailwindCSS · shadcn/ui · Zustand               | 流程配置、实时监控、全局状态管理                 |
| 后端      | uv Python 3.12 · FastAPI · LangGraph · Pydantic · SQLModel                  | REST / WebSocket API 与智能体运行引擎            |
| 模型      | **Ollama** (本地)                                                         | 统一模型抽象，连接本地Ollama服务                 |
| 工具      | **本地 Python 函数**                                                        | 直接在代码中定义，简化调用，专注核心逻辑           |
| 数据库     | **Sqlite**                                                                  | 零配置，通过SQLModel操作                       |
| 异步任务    | **FastAPI BackgroundTasks**                                                 | 处理异步运行，避免引入重量级队列                 |
| 部署      | **本地开发服务器** (`uv run` / `vite`)                                      | 快速启动，便于调试                           |
| 监控      | **标准日志 (Logging)**                                                      | 满足原型阶段的调试与追踪需求                   |

---

## 3. 总体架构

```mermaid
graph TD
    subgraph "用户浏览器"
        A[前端应用 (React + Vite)]
    end

    subgraph "后端服务 (FastAPI on UV)"
        B[REST API]
        C[WebSocket API]
        D[LangGraph 运行引擎]
        E[Ollama 适配器]
        F[Python 工具函数]
    end

    subgraph "基础设施"
        G[(Sqlite 数据库)]
        H[Ollama 服务]
    end

    A -- HTTP请求 --> B
    A -- WebSocket连接 --> C
    B -- 调用 --> D
    D -- 执行 --> F
    D -- 调用LLM --> E
    D -- 读写元数据 --> G
    E -- 请求 --> H
    D -- 推送事件 --> C
```

### 3.1 关键流程

1. **建模**：前端将拖拽生成的流程图序列化为 JSON 并提交 `/workflows` 创建；
2. **部署**：后端将 Workflow 编译为 LangGraph DAG，存储并标记为 *deployed*；
3. **运行**：触发 `/runs` 后，Orchestrator 按拓扑执行，每步产生事件推送 WebSocket；
4. **反思/修正**：Reflection Engine 分析节点输出，根据策略回写下一步输入或回滚；
5. **监控**：运行日志与指标写入 Redis / Prometheus，前端实时呈现。

---

## 4. 模块设计

### 4.1 前端

* **Workflow Designer**：基于 React Flow，支持节点模版、复制粘贴、对齐线；
* **Agent Registry**：Agent 列表、版本管理、启停、环境变量编辑；
* **Run Monitor**：树形视图 + 时序图查看节点事件；
* **Settings**：模型/工具/用户权限配置；

### 4.2 后端

| 模块               | 主要职责 (原型阶段)                        |
| ---------------- | -------------------------------------- |
| `api`            | FastAPI 控制层，定义REST/WebSocket接口。     |
| `models`         | SQLModel/Pydantic 定义实体。                |
| `services`       | 业务逻辑，如Agent/Workflow的CRUD。          |
| `runtime`        | LangGraph集成，构建/执行DAG，调用工具和模型。 |
| `core`           | 应用配置，数据库连接，Ollama客户端封装。      |

---

## 5. 数据模型 (简化)

```sql
-- Sqlite
CREATE TABLE agent (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    model_provider TEXT DEFAULT 'ollama',
    config JSON,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE workflow (
    id TEXT PRIMARY KEY,
    agent_id TEXT REFERENCES agent(id),
    graph JSON NOT NULL,
    version INTEGER DEFAULT 1,
    status TEXT DEFAULT 'draft',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE run (
    id TEXT PRIMARY KEY,
    workflow_id TEXT REFERENCES workflow(id),
    status TEXT,
    started_at TEXT,
    finished_at TEXT,
    metrics JSON
);
```

---

## 6. API 定义 (选摘)

### 6.1 创建 Agent

`POST /agents`

```jsonc
{
  "name": "cyber‑defender",
  "description": "企业内网安全检测智能体",
  "model_provider": "ollama",
  "config": {
    "temperature": 0.1,
    "system_prompt": "You are a security analyst AI..."
  }
}
```

### 6.2 创建 Workflow

`POST /workflows`

```jsonc
{
  "agent_id": "<agent‑uuid>",
  "graph": {
    "nodes": [
      {"id": "plan", "type": "PlannerNode", "next": "act"},
      {"id": "act", "type": "ToolNode", "tool": "nmap_scan", "next": "reflect"},
      {"id": "reflect", "type": "ReflectionNode", "next": "plan"}
    ]
  }
}
```

### 6.3 触发运行

`POST /runs`

```jsonc
{
  "workflow_id": "<workflow‑uuid>",
  "input": {
    "target": "********/24"
  }
}
```

> WebSocket `/ws/runs/{run_id}` 将持续推送事件：`RUN_STARTED` → `NODE_COMPLETED` → `REFLECTION_TRIGGERED` → … → `RUN_FINISHED`。

详细接口规范请参见 [附录 A](#附录A-接口列表)。

---

## 7. 示例流程

1. **安全扫描智能体**

   * 创建 Agent `cyber‑defender` →
   * 设计 Workflow：`Plan → NmapScan → Reflect → Report` →
   * 部署并运行，传入网段，系统自动多轮探测与报告汇总。

```mermaid
sequenceDiagram
    participant UI
    participant API
    participant Engine
    UI->>API: POST /runs
    API->>Engine: enqueue run
    Engine-->>UI: WS RUN_STARTED
    Engine->>ToolAdapter: call nmap_scan
    ToolAdapter-->>Engine: scan result
    Engine->>Reflection: analyze quality
    Reflection-->>Engine: need extra scan?
    Engine-->>UI: WS NODE_COMPLETED / RUN_FINISHED
```

---

## 8. 本地工具调用展示

*   **定义方式**：在后端代码中直接定义 Python 函数。
*   **调用方式**：LangGraph 引擎在执行时直接调用相应的函数。
*   **日志记录**：通过标准日志模块记录工具的名称、输入、输出和执行状态。
*   **前端呈现**：在运行详情页中，以文本形式展示每一步调用的工具及其结果。

---

## 9. Reflection 策略 (原型)

*   **简化实现**：在原型阶段，Reflection 将通过 **预定义的条件分支** 来实现。
*   **示例**：在 Workflow 的 JSON 定义中，可以设置一个节点的后继依赖于上一个节点输出的特定内容（例如，检查输出中是否包含 "ERROR" 关键词）。
*   **目标**：主要用于演示工作流的循环与修正能力，而非实现复杂的评估逻辑。

---

## 10. 多模型切换

| 配置项               | 描述           | 示例                       |
| ----------------- | ------------ | ------------------------ |
| `MODEL_PROVIDER`  | 当前使用的模型      | `ollama` / `openai`      |
| `OLLAMA_ENDPOINT` | 本地 Ollama 地址 | `http://10.0.0.6:11434` |
| `OPENAI_API_KEY`  | OpenAI Key   | `sk‑...`                 |

使用 Feature Flag 热加载，无需重启服务。


---

> **文档版本：v1.1 (原型) — 2025‑06‑09**

---

## 11. 原型开发步骤

### 第一阶段：后端核心搭建 (Foundation)

1.  **项目结构初始化**: 创建 `app` 目录及 `api`, `models`, `services`, `core`, `runtime` 等子模块。
2.  **数据库与模型定义**: 使用 `SQLModel` 定义 `Agent`, `Workflow`, `Run` 表，配置 `Sqlite` 连接。
3.  **核心API开发**: 使用 `FastAPI` 实现 Agent 和 Workflow 的基础 CRUD 端点。

### 第二阶段：LangGraph 与 Ollama 集成 (Core Logic)

1.  **Ollama集成**: 创建一个 Provider/Adapter 与 Ollama 服务通信。
2.  **定义简单工具**: 在 `runtime/tools` 中定义简单的 Python 函数作为工具。
3.  **LangGraph运行引擎**: 开发 `WorkflowRunner` 服务，将 JSON 动态编译为 LangGraph 实例，并通过 `BackgroundTasks` 异步执行。

### 第三阶段：前端基础界面 (UI)

1.  **前端项目初始化**: 使用 `Vite` + `React` + `TypeScript` 搭建项目，集成 `TailwindCSS`。
2.  **Agent/Workflow 管理页面**: 实现 Agent 和 Workflow 的列表展示与表单创建/编辑功能 (Workflow 定义暂时使用 JSON 文本域)。
3.  **运行与监控页面**: 实现运行历史列表和简单的运行详情页，用于展示节点状态。

### 第四阶段：端到端流程联调与展示 (E2E Demo)

1.  **API联调**: 对接前后端所有接口。
2.  **WebSocket集成**: 后端在工作流执行时推送事件，前端实时更新UI。
3.  **示例工作流**: 设计并实现一个完整的端到端示例（如“研究报告助手”），清晰展示 Agentic Workflow 全流程。
