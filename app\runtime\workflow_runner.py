from langgraph.graph import StateGraph, <PERSON><PERSON>
from typing import TypedDict, Annotated, List
import operator
from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage, ToolMessage

from app.runtime import tools
from app.core.llm import get_llm

# 1. Define the state for our graph
class AgentState(TypedDict):
    """
    Represents the state of our agent.
    """
    messages: Annotated[List[AnyMessage], operator.add]


class WorkflowRunner:
    """
    Compiles and runs a workflow defined in JSON format using LangGraph.
    """
    def __init__(self, workflow_def: dict):
        self.workflow_def = workflow_def
        self.llm = get_llm()
        self.tool_registry = {
            "get_current_time": tools.get_current_time,
        }
        self.graph = self._compile()

    def _agent_node(self, state: AgentState):
        """A node that calls the LLM."""
        return {"messages": [self.llm.invoke(state["messages"])]}

    def _tool_node(self, state: AgentState):
        """A node that executes a tool."""
        # This is a simplified implementation for the prototype
        tool_name = "get_current_time" # Hardcoded for now
        result = self.tool_registry[tool_name]()
        message = ToolMessage(content=str(result), name=tool_name, tool_call_id="1") # Dummy ID
        return {"messages": [message]}

    def _compile(self):
        """
        Compiles the JSON workflow definition into an executable LangGraph.
        """
        workflow = StateGraph(AgentState)

        # For the prototype, we'll use a hardcoded graph structure
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tool", self._tool_node)

        workflow.set_entry_point("agent")
        workflow.add_edge("agent", "tool")
        workflow.add_edge("tool", END)

        return workflow.compile()

    async def run(self, inputs: dict):
        """
        Runs the compiled graph with the given inputs.
        """
        initial_messages = [
            SystemMessage(content="You are a helpful assistant."),
            HumanMessage(content=inputs.get("question", "Hello, what time is it?"))
        ]
        final_state = None
        async for output in self.graph.astream({"messages": initial_messages}):
            final_state = output
        return final_state