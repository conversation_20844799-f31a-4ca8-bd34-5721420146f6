import { useState, useEffect } from "react";
import { Button } from "./ui/button";

const API_BASE_URL = "http://localhost:8001";

interface Run {
  id: string;
  status: string;
  metrics: {
    output?: any[];
  };
}

export function RunMonitor({ workflowId }: { workflowId: string }) {
  const [run, setRun] = useState<Run | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  const triggerRun = async () => {
    const response = await fetch(`${API_BASE_URL}/runs/?workflow_id=${workflowId}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ question: "Hello, what time is it?" }),
    });
    const data = await response.json();
    setRun(data);
    setIsPolling(true);
  };

  useEffect(() => {
    if (!isPolling || !run || run.status === "completed" || run.status === "failed") {
      setIsPolling(false);
      return;
    }

    const intervalId = setInterval(async () => {
      const response = await fetch(`${API_BASE_URL}/runs/${run.id}`);
      const data = await response.json();
      setRun(data);
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(intervalId);
  }, [isPolling, run]);

  return (
    <div className="p-6 bg-card border rounded-lg">
      <div className="flex justify-between items-center">
        <h3 className="font-semibold text-card-foreground">Workflow Runner</h3>
        <Button onClick={triggerRun} disabled={isPolling} size="sm">
          {isPolling ? "Running..." : "Run"}
        </Button>
      </div>
      {run && (
        <div className="mt-4 space-y-2">
          <p className="text-sm text-muted-foreground truncate">Run ID: {run.id}</p>
          <div className="flex items-center">
            <p className="text-sm text-muted-foreground mr-2">Status:</p>
            <span className={`text-sm font-semibold ${
              run.status === 'completed' ? 'text-green-400' :
              run.status === 'failed' ? 'text-red-400' : 'text-yellow-400'
            }`}>
              {run.status}
            </span>
          </div>
          {run.status === "completed" && run.metrics.output && (
            <div className="mt-4 p-3 bg-secondary rounded">
              <h4 className="font-semibold text-secondary-foreground text-sm">Final Output:</h4>
              <pre className="text-xs whitespace-pre-wrap text-muted-foreground mt-2">
                {JSON.stringify(run.metrics.output, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}