import { useEffect, useState } from "react";

// Define the type for an Agent based on our backend model
interface Agent {
  id: string;
  name: string;
  description: string | null;
  model_provider: string;
}

const API_BASE_URL = "http://localhost:8001";

export function AgentList({ refreshSignal }: { refreshSignal: number }) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAgents() {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/agents/`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setAgents(data);
      } catch (e) {
        if (e instanceof Error) {
          setError(e.message);
        } else {
          setError("An unknown error occurred");
        }
      } finally {
        setLoading(false);
      }
    }

    fetchAgents();
  }, [refreshSignal]);

  if (loading) {
    return <div className="text-center p-4">Loading agents...</div>;
  }

  if (error) {
    return <div className="text-center p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="space-y-4">
      {agents.length === 0 && !loading ? (
        <p className="text-center text-muted-foreground">No agents found. Create one above to get started.</p>
      ) : (
        agents.map((agent) => (
          <div key={agent.id} className="p-4 bg-card border rounded-lg flex justify-between items-center">
            <div>
              <h3 className="font-semibold text-card-foreground">{agent.name}</h3>
              <p className="text-sm text-muted-foreground">{agent.description || "No description"}</p>
            </div>
            <div className="text-xs text-muted-foreground">
              {agent.model_provider}
            </div>
          </div>
        ))
      )}
    </div>
  );
}