from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.core.database import create_db_and_tables

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Code to run on startup
    print("Starting up...")
    create_db_and_tables()
    print("Database tables created.")
    yield
    # Code to run on shutdown
    print("Shutting down...")

app = FastAPI(
    title="Web Agent Framework",
    description="A framework for building and managing agentic workflows.",
    version="0.1.0 (Prototype)",
    lifespan=lifespan
)

# CORS Middleware Configuration
origins = [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:3000", # Common port for React dev servers
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", tags=["Health Check"])
def read_root():
    """
    Root endpoint to check if the API is running.
    """
    return {"status": "ok", "message": "Welcome to the Web Agent Framework API!"}

# Include routers from the app.api module
from app.api import agents, workflows, runs
app.include_router(agents.router)
app.include_router(workflows.router)
app.include_router(runs.router)
