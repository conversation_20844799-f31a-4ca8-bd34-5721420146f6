from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select

from app.core.database import engine
from app.models.models import Agent


def get_session():
    with Session(engine) as session:
        yield session


router = APIRouter(
    prefix="/agents",
    tags=["Agents"],
)


@router.post("/", response_model=Agent)
def create_agent(*, session: Session = Depends(get_session), agent: Agent):
    """
    Create a new agent.
    """
    session.add(agent)
    session.commit()
    session.refresh(agent)
    return agent


@router.get("/", response_model=List[Agent])
def read_agents(
    *,
    session: Session = Depends(get_session),
    skip: int = 0,
    limit: int = 100,
):
    """
    Get a list of all agents.
    """
    agents = session.exec(select(Agent).offset(skip).limit(limit)).all()
    return agents