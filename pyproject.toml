[project]
name = "neoagent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.115.12",
    "sqlmodel>=0.0.18",
    "langchain>=0.3.25",
    "langchain-community>=0.3.24",
    "langchain-ollama>=0.3.3",
    "langgraph>=0.4.8",
    "uvicorn>=0.34.3",
]

[[tool.uv.index]]
url = "https://pypi.mirrors.ustc.edu.cn/simple"
default = true
