from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select

from app.core.database import engine
from app.models.models import Workflow


def get_session():
    with Session(engine) as session:
        yield session


router = APIRouter(
    prefix="/workflows",
    tags=["Workflows"],
)


@router.post("/", response_model=Workflow)
def create_workflow(*, session: Session = Depends(get_session), workflow: Workflow):
    """
    Create a new workflow.
    """
    session.add(workflow)
    session.commit()
    session.refresh(workflow)
    return workflow


@router.get("/", response_model=List[Workflow])
def read_workflows(
    *,
    session: Session = Depends(get_session),
    skip: int = 0,
    limit: int = 100,
):
    """
    Get a list of all workflows.
    """
    workflows = session.exec(select(Workflow).offset(skip).limit(limit)).all()
    return workflows