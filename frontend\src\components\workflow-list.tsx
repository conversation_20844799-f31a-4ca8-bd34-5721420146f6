import { useEffect, useState } from "react";

interface Workflow {
  id: string;
  agent_id: string | null;
  status: string;
  version: number;
}

const API_BASE_URL = "http://localhost:8001";

export function WorkflowList() {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchWorkflows() {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/workflows/`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setWorkflows(data);
      } catch (e) {
        if (e instanceof Error) {
          setError(e.message);
        } else {
          setError("An unknown error occurred");
        }
      } finally {
        setLoading(false);
      }
    }

    fetchWorkflows();
  }, []);

  if (loading) {
    return <div className="text-center p-4">Loading workflows...</div>;
  }

  if (error) {
    return <div className="text-center p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="space-y-4">
       {workflows.length === 0 && !loading ? (
        <p className="text-center text-muted-foreground">No workflows found yet.</p>
      ) : (
        workflows.map((workflow) => (
          <div key={workflow.id} className="p-4 bg-card border rounded-lg">
            <h3 className="font-semibold text-card-foreground truncate">ID: {workflow.id}</h3>
            <p className="text-sm text-muted-foreground">Agent: {workflow.agent_id || "N/A"}</p>
            <div className="mt-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary text-secondary-foreground">
                {workflow.status}
              </span>
            </div>
          </div>
        ))
      )}
    </div>
  );
}