from sqlmodel import SQLModel, create_engine

# Define the database URL for a local SQLite file
DATABASE_URL = "sqlite:///./database.db"

# Create the database engine
# connect_args is specific to SQLite to allow multiple threads
engine = create_engine(DATABASE_URL, echo=True, connect_args={"check_same_thread": False})


def create_db_and_tables():
    """
    Initializes the database and creates all tables based on SQLModel metadata.
    """
    # This will create the tables if they don't exist
    SQLModel.metadata.create_all(engine)